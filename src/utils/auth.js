const API_BASE_URL = 'http://localhost:3001/api'

export const login = async (username, password) => {
  try {
    const response = await fetch(`${API_BASE_URL}/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ username, password }),
    })

    const data = await response.json()

    if (!response.ok) {
      throw new Error(data.error || 'Login failed')
    }

    // Store auth data in localStorage
    localStorage.setItem('auth', JSON.stringify(data))
    
    return data
  } catch (error) {
    throw error
  }
}

export const getStoredAuth = () => {
  try {
    const auth = localStorage.getItem('auth')
    return auth ? JSON.parse(auth) : null
  } catch (error) {
    console.error('Error parsing stored auth:', error)
    return null
  }
}

export const clearAuth = () => {
  localStorage.removeItem('auth')
}

export const getAuthHeaders = () => {
  const auth = getStoredAuth()
  return auth ? {
    'Authorization': `Bearer ${auth.token}`,
    'Content-Type': 'application/json'
  } : {
    'Content-Type': 'application/json'
  }
}

export const fetchWithAuth = async (url, options = {}) => {
  const headers = getAuthHeaders()
  
  const response = await fetch(`${API_BASE_URL}${url}`, {
    ...options,
    headers: {
      ...headers,
      ...options.headers
    }
  })

  if (response.status === 401) {
    clearAuth()
    window.location.href = '/login'
    return
  }

  return response
}
