import { useState, useEffect } from 'react'
import { fetchWithAuth } from '../utils/auth'

const AdminDashboard = ({ user, onLogout }) => {
  const [users, setUsers] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [activeTab, setActiveTab] = useState('overview')

  useEffect(() => {
    fetchUsers()
  }, [])

  const fetchUsers = async () => {
    try {
      setLoading(true)
      const response = await fetchWithAuth('/users')
      
      if (!response) return // Redirected to login
      
      if (!response.ok) {
        throw new Error('Failed to fetch users')
      }
      
      const usersData = await response.json()
      setUsers(usersData)
    } catch (error) {
      setError(error.message)
    } finally {
      setLoading(false)
    }
  }

  const handleDashboardClick = () => {
    setActiveTab('overview')
  }

  const renderTaskManagement = () => {
    return (
      <>
        {/* Task Filters and Controls */}
        <div className="dashboard-section">
          <div className="task-header">
            <h2>Task Management</h2>
            <button className="action-button primary">Create New Task</button>
          </div>

          <div className="task-filters">
            <div className="filter-group">
              <label>Date Filter:</label>
              <input type="date" className="date-filter" defaultValue={new Date().toISOString().split('T')[0]} />
            </div>
            <div className="filter-group">
              <label>Status:</label>
              <select className="status-filter">
                <option value="all">All Tasks</option>
                <option value="pending">Pending</option>
                <option value="in-progress">In Progress</option>
                <option value="completed">Completed</option>
              </select>
            </div>
            <div className="filter-group">
              <label>Employee:</label>
              <select className="employee-filter">
                <option value="all">All Employees</option>
                <option value="tom">Tom Smith</option>
                <option value="john">John Johnson</option>
                <option value="mike">Mike Wilson</option>
              </select>
            </div>
          </div>
        </div>

        {/* Task List */}
        <div className="dashboard-section">
          <h3>Today's Tasks</h3>
          <div className="admin-tasks-container">
            <div className="admin-task-card pending">
              <div className="task-header-info">
                <h4>Kitchen Repair</h4>
                <span className="task-status">Pending</span>
              </div>
              <div className="task-details">
                <div className="customer-info">
                  <strong>Customer:</strong> Mrs. Johnson<br/>
                  <strong>Address:</strong> 123 High Street, London<br/>
                  <strong>Phone:</strong> 020 7123 4567
                </div>
                <div className="service-info">
                  <strong>Service:</strong> Plumbing<br/>
                  <strong>Description:</strong> Fix leaking tap and replace cabinet door<br/>
                  <strong>Estimated Time:</strong> 2-3 hours
                </div>
                <div className="assignment-info">
                  <strong>Assigned to:</strong> Tom Smith<br/>
                  <strong>Scheduled:</strong> Today, 09:00 AM
                </div>
              </div>
              <div className="task-actions">
                <button className="btn-edit">Edit</button>
                <button className="btn-delete">Delete</button>
                <button className="btn-assign">Reassign</button>
              </div>
            </div>

            <div className="admin-task-card in-progress">
              <div className="task-header-info">
                <h4>Bathroom Installation</h4>
                <span className="task-status">In Progress</span>
              </div>
              <div className="task-details">
                <div className="customer-info">
                  <strong>Customer:</strong> Mr. Williams<br/>
                  <strong>Address:</strong> 456 Oak Avenue, London<br/>
                  <strong>Phone:</strong> 020 7987 6543
                </div>
                <div className="service-info">
                  <strong>Service:</strong> Installation<br/>
                  <strong>Description:</strong> Install new shower unit and tiles<br/>
                  <strong>Estimated Time:</strong> 4-5 hours
                </div>
                <div className="assignment-info">
                  <strong>Assigned to:</strong> John Johnson<br/>
                  <strong>Started:</strong> Today, 08:30 AM
                </div>
              </div>
              <div className="task-actions">
                <button className="btn-edit">Edit</button>
                <button className="btn-delete">Delete</button>
                <button className="btn-view">View Progress</button>
              </div>
            </div>

            <div className="admin-task-card completed">
              <div className="task-header-info">
                <h4>Garden Fence Repair</h4>
                <span className="task-status">Completed</span>
              </div>
              <div className="task-details">
                <div className="customer-info">
                  <strong>Customer:</strong> Mrs. Davis<br/>
                  <strong>Address:</strong> 789 Elm Road, London<br/>
                  <strong>Phone:</strong> 020 7456 7890
                </div>
                <div className="service-info">
                  <strong>Service:</strong> Repair<br/>
                  <strong>Description:</strong> Replace damaged fence panels<br/>
                  <strong>Actual Time:</strong> 3 hours
                </div>
                <div className="assignment-info">
                  <strong>Completed by:</strong> Mike Wilson<br/>
                  <strong>Finished:</strong> Today, 02:30 PM
                </div>
              </div>
              <div className="task-actions">
                <button className="btn-view">View Details</button>
                <button className="btn-invoice">Generate Invoice</button>
              </div>
            </div>
          </div>
        </div>

        {/* Employee Task Overview */}
        <div className="dashboard-section">
          <h3>Employee Task Overview</h3>
          <div className="employee-overview">
            <div className="employee-card">
              <h4>Tom Smith</h4>
              <div className="employee-stats">
                <span className="stat">Today: 2 tasks</span>
                <span className="stat">Pending: 1</span>
                <span className="stat">Completed: 1</span>
              </div>
              <button className="btn-view-employee">View Tasks</button>
            </div>
            <div className="employee-card">
              <h4>John Johnson</h4>
              <div className="employee-stats">
                <span className="stat">Today: 1 task</span>
                <span className="stat">In Progress: 1</span>
                <span className="stat">Completed: 0</span>
              </div>
              <button className="btn-view-employee">View Tasks</button>
            </div>
            <div className="employee-card">
              <h4>Mike Wilson</h4>
              <div className="employee-stats">
                <span className="stat">Today: 1 task</span>
                <span className="stat">Pending: 0</span>
                <span className="stat">Completed: 1</span>
              </div>
              <button className="btn-view-employee">View Tasks</button>
            </div>
          </div>
        </div>
      </>
    )
  }

  const renderContent = () => {
    switch (activeTab) {
      case 'tasks':
        return renderTaskManagement()
      case 'statistics':
        return (
          <div className="dashboard-section">
            <h2>Statistics</h2>
            <p>Statistics and reports coming soon...</p>
          </div>
        )
      case 'archive':
        return (
          <div className="dashboard-section">
            <h2>Archive</h2>
            <p>Archive functionality coming soon...</p>
          </div>
        )
      default:
        return (
          <>
            <div className="dashboard-section">
              <h2>System Overview</h2>
              <div className="stats-grid">
                <div className="stat-card">
                  <h3>Total Users</h3>
                  <p className="stat-number">{users.length}</p>
                </div>
                <div className="stat-card">
                  <h3>Employees</h3>
                  <p className="stat-number">{users.filter(u => u.role === 'employee').length}</p>
                </div>
                <div className="stat-card">
                  <h3>Admins</h3>
                  <p className="stat-number">{users.filter(u => u.role === 'admin').length}</p>
                </div>
              </div>
            </div>

            <div className="dashboard-section">
              <h2>User Management</h2>
              {loading ? (
                <p>Loading users...</p>
              ) : error ? (
                <p className="error-message">Error: {error}</p>
              ) : (
                <div className="users-table-container">
                  <table className="users-table">
                    <thead>
                      <tr>
                        <th>ID</th>
                        <th>Username</th>
                        <th>Full Name</th>
                        <th>Role</th>
                        <th>Created</th>
                      </tr>
                    </thead>
                    <tbody>
                      {users.map(user => (
                        <tr key={user.id}>
                          <td>{user.id}</td>
                          <td>{user.username}</td>
                          <td>{user.full_name}</td>
                          <td>
                            <span className={`role-badge ${user.role}`}>
                              {user.role}
                            </span>
                          </td>
                          <td>{new Date(user.created_at).toLocaleDateString()}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </div>

            <div className="dashboard-section">
              <h2>Quick Actions</h2>
              <div className="actions-grid">
                <button className="action-button">Add New Employee</button>
                <button className="action-button">View Reports</button>
                <button className="action-button">System Settings</button>
                <button className="action-button">Backup Data</button>
              </div>
            </div>
          </>
        )
    }
  }

  return (
    <div className="dashboard">
      <header className="dashboard-header">
        <h1 className="dashboard-title" onClick={handleDashboardClick}>Dashboard</h1>
        <div className="user-info">
          <span>Welcome, {user.full_name}</span>
          <button onClick={onLogout} className="logout-button">Logout</button>
        </div>
      </header>

      <nav className="dashboard-nav">
        <button
          className={`nav-button ${activeTab === 'overview' ? 'active' : ''}`}
          onClick={() => setActiveTab('overview')}
        >
          Overview
        </button>
        <button
          className={`nav-button ${activeTab === 'tasks' ? 'active' : ''}`}
          onClick={() => setActiveTab('tasks')}
        >
          Tasks
        </button>
        <button
          className={`nav-button ${activeTab === 'statistics' ? 'active' : ''}`}
          onClick={() => setActiveTab('statistics')}
        >
          Statistics
        </button>
        <button
          className={`nav-button ${activeTab === 'archive' ? 'active' : ''}`}
          onClick={() => setActiveTab('archive')}
        >
          Archive
        </button>
      </nav>

      <main className="dashboard-content">
        {renderContent()}
      </main>
    </div>
  )
}

export default AdminDashboard
