import { useState, useEffect } from 'react'

const EmployeeDashboard = ({ user, onLogout }) => {
  const [currentTime, setCurrentTime] = useState(new Date())
  const [activeTab, setActiveTab] = useState('tasks')

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date())
    }, 1000)

    return () => clearInterval(timer)
  }, [])

  const formatTime = (date) => {
    return date.toLocaleTimeString('en-GB', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
  }

  const formatDate = (date) => {
    return date.toLocaleDateString('en-GB', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const handleDashboardClick = () => {
    setActiveTab('overview')
  }

  const renderContent = () => {
    switch (activeTab) {
      case 'tasks':
        return (
          <div className="dashboard-section">
            <h2>My Tasks</h2>
            <div className="tasks-container">
              <div className="task-card pending">
                <h4>Kitchen Repair - 123 High Street</h4>
                <p>Fix leaking tap and replace cabinet door</p>
                <span className="task-status">Pending</span>
              </div>
              <div className="task-card in-progress">
                <h4>Bathroom Installation - 456 Oak Avenue</h4>
                <p>Install new shower unit and tiles</p>
                <span className="task-status">In Progress</span>
              </div>
              <div className="task-card completed">
                <h4>Garden Fence - 789 Elm Road</h4>
                <p>Replace damaged fence panels</p>
                <span className="task-status">Completed</span>
              </div>
            </div>
          </div>
        )
      default:
        return (
          <>
            <div className="dashboard-section">
              <h2>Today's Overview</h2>
              <div className="time-display">
                <div className="current-time">
                  <h3>{formatTime(currentTime)}</h3>
                  <p>{formatDate(currentTime)}</p>
                </div>
              </div>
            </div>

            <div className="dashboard-section">
              <h2>My Tasks</h2>
              <div className="tasks-container">
                <div className="task-card pending">
                  <h4>Kitchen Repair - 123 High Street</h4>
                  <p>Fix leaking tap and replace cabinet door</p>
                  <span className="task-status">Pending</span>
                </div>
                <div className="task-card in-progress">
                  <h4>Bathroom Installation - 456 Oak Avenue</h4>
                  <p>Install new shower unit and tiles</p>
                  <span className="task-status">In Progress</span>
                </div>
                <div className="task-card completed">
                  <h4>Garden Fence - 789 Elm Road</h4>
                  <p>Replace damaged fence panels</p>
                  <span className="task-status">Completed</span>
                </div>
              </div>
            </div>

            <div className="dashboard-section">
              <h2>Quick Actions</h2>
              <div className="actions-grid">
                <button className="action-button">Clock In/Out</button>
                <button className="action-button">View Schedule</button>
                <button className="action-button">Report Issue</button>
                <button className="action-button">Update Task Status</button>
              </div>
            </div>

            <div className="dashboard-section">
              <h2>Recent Activity</h2>
              <div className="activity-list">
                <div className="activity-item">
                  <span className="activity-time">09:30</span>
                  <span className="activity-text">Started task at 123 High Street</span>
                </div>
                <div className="activity-item">
                  <span className="activity-time">08:45</span>
                  <span className="activity-text">Clocked in for the day</span>
                </div>
                <div className="activity-item">
                  <span className="activity-time">Yesterday</span>
                  <span className="activity-text">Completed bathroom installation</span>
                </div>
              </div>
            </div>
          </>
        )
    }
  }

  return (
    <div className="dashboard">
      <header className="dashboard-header">
        <h1 className="dashboard-title" onClick={handleDashboardClick}>Dashboard</h1>
        <div className="user-info">
          <span>Welcome, {user.full_name}</span>
          <button onClick={onLogout} className="logout-button">Logout</button>
        </div>
      </header>

      <nav className="dashboard-nav">
        <button
          className={`nav-button ${activeTab === 'overview' ? 'active' : ''}`}
          onClick={() => setActiveTab('overview')}
        >
          Overview
        </button>
        <button
          className={`nav-button ${activeTab === 'tasks' ? 'active' : ''}`}
          onClick={() => setActiveTab('tasks')}
        >
          Tasks
        </button>
      </nav>

      <main className="dashboard-content">
        {renderContent()}
      </main>
    </div>
  )
}

export default EmployeeDashboard
